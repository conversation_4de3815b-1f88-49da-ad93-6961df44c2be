@import '../variables';

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: transparent;
    border-bottom: 1px solid transparent;
  z-index: 100;
  padding: $spacing-md 0;
    transition: all ease 0.3s;
    box-shadow: none;




  &--scrolled {
    transition: all ease 0.3s;
    background-color: transparent;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #242527;
    box-shadow: inset 0px -4px 12px -4px $brand-glow-strong;

  }

  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-primary;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: $spacing-xs;
  }

  &__nav {
    display: flex;
    gap: $spacing-xl;

    @media (max-width: $breakpoint-md) {
      display: none;
    }
  }

  &__link {
    color: $text-secondary;
    text-decoration: none;
    transition: $button-transition;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: $button-radius;
    background: transparent;
    font-weight: 400;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.06);
      border-radius: $button-radius;
      opacity: 0;
      transition: $button-transition;
      z-index: -1;
    }

    &:hover {
      color: $text-primary;
      transform: translateY(-1px);

      &::before {
        opacity: 1;
      }
    }

    &--active {
      color: $brand-primary;
      font-weight: 500;

      &::before {
        background: $brand-glow-light;
        opacity: 1;
      }
    }
  }

  &__button {
    background: transparent;
    border: none;
    padding: 0.5rem 1rem;
    font: inherit;
    cursor: pointer;
    border-radius: $button-radius;
    color: $text-primary;
    transition: $button-transition;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.06);
      border-radius: $button-radius;
      opacity: 0;
      transition: $button-transition;
      z-index: -1;
    }

    &:hover {
      transform: translateY(-1px);

      &::before {
        opacity: 1;
      }
    }
  }
}