import { Resend } from 'resend'
import { NextResponse } from 'next/server'

const resend = new Resend(process.env.RESEND_API_KEY)

export async function POST(req: Request) {
  try {
    const { firstName, email, message } = await req.json()

    await resend.emails.send({
      from: 'Portfolio! <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: `Nuevo mensaje de ${firstName}`,
      text: `Email: ${email}\n\nMensaje: \n${message}`,
    })

    return NextResponse.json({ success: true })
  } catch (err) {
    console.log(err)
    return NextResponse.json({ error: 'Error enviando mail' }, { status: 500 })
  }
}
