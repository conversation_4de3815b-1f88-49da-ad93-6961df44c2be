.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;

  .split-word {
    display: inline-block;
    opacity: 0;
    will-change: transform, opacity;
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: rgba(14, 14, 14, 0.7); // opcional para oscurecer el fondo
    background: linear-gradient(to bottom, #001f3fdc 0%, #00000000 100%);

    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 2rem;
    z-index: 1;
  }

  .container {
    max-width: 960px;
    text-align: center;
    color: white;
    z-index: 2;
  }

  .heading--xl {
    font-size: 3rem;
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #ccc;
  }

  .hero__buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;

    .button {
      padding: 0.75rem 1.5rem;
      border: 2px solid rgba(138, 158, 255, 1);
      border-radius: 4px;
      text-decoration: none;
      color: white;
      transition: background 0.3s;

      &--primary {
        background: linear-gradient(90deg, #00c6ff, #0072ff);
        background: rgba(138, 158, 255, 1);
        color: #0e0e0e;
        font-weight: bold;
      }

      &:hover {
        background: white;
        color: black;
      }
    }
  }
}