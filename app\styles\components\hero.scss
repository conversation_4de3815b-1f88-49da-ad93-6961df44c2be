@import '../variables';

.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;

  .split-word {
    display: inline-block;
    opacity: 0;
    will-change: transform, opacity;
  }

  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: rgba(14, 14, 14, 0.7); // opcional para oscurecer el fondo
    background: linear-gradient(to bottom, #001f3fdc 0%, #00000000 100%);

    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 2rem;
    z-index: 1;
  }

  .container {
    max-width: 960px;
    text-align: center;
    color: white;
    z-index: 2;
  }

  .heading--xl {
    font-size: 3rem;
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #ccc;
  }

  .hero__buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;

    .button {
      padding: 15px 24px;
      border: $button-border;
      border-radius: $button-radius;
      text-decoration: none;
      color: white;
      transition: $button-transition;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: $button-shadow;
      background: rgba($bg-secondary, 0.6);
      font-size: 1rem;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba($bg-secondary, 0.8);
        transition: $button-transition;
        z-index: -1;
        opacity: 0;
      }

      &--primary {
        background: $brand-gradient;
        color: white;
        font-weight: 500;
        border: none;

        &::before {
          background: rgba(255, 255, 255, 0.1);
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: $button-shadow-hover, 0 0 15px $brand-glow-light;

          &::before {
            opacity: 1;
          }
        }
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: $button-shadow-hover;
        color: white;

        &::before {
          opacity: 1;
        }
      }
    }

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
    }
  }
}