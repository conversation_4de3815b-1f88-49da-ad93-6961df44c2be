@import 'variables';
@import url('https://api.fontshare.com/v2/css?f[]=space-grotesk@400,500,700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Space Grotesk', sans-serif;
  background-color: $bg-primary;
  color: $text-primary;
  line-height: 1.6;
  cursor: none;
  background-color: black;
}

.container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $spacing-lg;
}

.section {
  padding: $spacing-3xl 0;
}

.button {
  display: inline-block;
  padding: $spacing-sm $spacing-lg;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;

  &--primary {
    background-color: $accent-blue;
    color: $text-primary;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($accent-blue, 0.3);
    }
  }
}

.heading {
  font-weight: 700;
  margin-bottom: $spacing-lg;

  &--xl {
    font-size: 3.5rem;
    line-height: 1.1;

    @media (max-width: $breakpoint-md) {
      font-size: 2.5rem;
    }
  }

  &--lg {
    font-size: 2.5rem;

    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
    }
  }
}

#rocket-cursor {
  filter: drop-shadow(0 0 6px rgba(255, 100, 100, 0.6));
  transition: transform 0.15s ease;
  will-change: transform;
}

.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 0;

  .star {
    position: absolute;
    width: 3px;
    height: 3px;
    background: white;
    border-radius: 50%;
    animation: floatStar linear infinite;
    opacity: 0.8;

    // distribución y animación aleatoria
    @for $i from 1 through 30 {
      &:nth-child(#{$i}) {
        top: random(100) * 1%;
        left: random(100) * 1%;
        animation-duration: #{5 + random(10)}s;
        animation-delay: -#{random(10)}s;
        transform: scale(#{0.5 + random(5) / 10});
      }
    }
  }
}

@keyframes floatStar {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }

  50% {
    transform: translateY(70px) scale(1.1);
    opacity: 1;
  }

  100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
}