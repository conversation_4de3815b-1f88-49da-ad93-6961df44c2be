@import 'variables';
@import url('https://api.fontshare.com/v2/css?f[]=space-grotesk@400,500,700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Space Grotesk', sans-serif;
  background-color: $bg-primary;
  color: $text-primary;
  line-height: 1.6;
  cursor: none;
  background-color: black;
}

.container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $spacing-lg;
}

.section {
  padding: $spacing-3xl 0;
}

.button {
  display: inline-block;
  padding: $spacing-sm $spacing-lg;
  border-radius: $button-radius;
  font-weight: 500;
  transition: $button-transition;
  cursor: pointer;
  border: $button-border;
  text-decoration: none;
  background: transparent;
  color: $text-primary;
  box-shadow: $button-shadow;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.04);
    transition: $button-transition;
    z-index: -1;
    opacity: 0;
  }

  &:hover {
    transform: translateY(-1px);
    border: $button-border-hover;
    box-shadow: $button-shadow-hover;
    color: white;

    &::before {
      opacity: 1;
    }
  }

  &--primary {
    background: $brand-gradient;
    color: white;
    border: none;

    &::before {
      background: rgba(255, 255, 255, 0.1);
      opacity: 0;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: $button-shadow-hover, 0 0 15px $brand-glow-light;
      border: none;

      &::before {
        opacity: 1;
      }
    }
  }

  &--secondary {
    background: rgba($bg-secondary, 0.6);
    border: $button-border;
    color: $text-primary;

    &:hover {
      background: rgba($bg-secondary, 0.8);
      transform: translateY(-1px);
      box-shadow: $button-shadow-hover;
      border: $button-border-hover;
    }
  }
}

.heading {
  font-weight: 700;
  margin-bottom: $spacing-lg;

  &--xl {
    font-size: 3.5rem;
    line-height: 1.1;

    @media (max-width: $breakpoint-md) {
      font-size: 2.5rem;
    }
  }

  &--lg {
    font-size: 2.5rem;

    @media (max-width: $breakpoint-md) {
      font-size: 2rem;
    }
  }
}

#rocket-cursor {
  filter: drop-shadow(0 0 6px $brand-glow);
  transition: transform 0.15s ease;
  will-change: transform;
}

.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 0;

  .star {
    position: absolute;
    width: 3px;
    height: 3px;
    background: white;
    border-radius: 50%;
    animation: floatStar linear infinite;
    opacity: 0.8;

    // distribución y animación aleatoria
    @for $i from 1 through 30 {
      &:nth-child(#{$i}) {
        top: random(100) * 1%;
        left: random(100) * 1%;
        animation-duration: #{5 + random(10)}s;
        animation-delay: -#{random(10)}s;
        transform: scale(#{0.5 + random(5) / 10});
      }
    }
  }
}

.button {
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(138, 158, 255, 1);
  border-radius: 4px;
  text-decoration: none;
  color: white;
  transition: background 0.3s;

  &--primary {
    background: linear-gradient(90deg, #00c6ff, #0072ff);
    background: rgba(138, 158, 255, 1);
    color: #0e0e0e;
    font-weight: bold;
  }

  &:hover {
    background: white;
    color: black;
  }
}

@keyframes floatStar {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }

  50% {
    transform: translateY(70px) scale(1.1);
    opacity: 1;
  }

  100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
}

// Import component styles
@import 'components/toast';