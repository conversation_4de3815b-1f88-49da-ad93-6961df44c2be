.howWork {
    position: relative;

    .scroll-container {
        position: relative;
        width: 100%;
        //   height: 100%;
        overflow-x: hidden;
        scroll-snap-type: y mandatory;
        overflow-y: scroll;
        height: 100vh;

        .rocket-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            z-index: 10;

            img {
                width: 100%;
                transition: transform 0.5s ease;
            }
        }

        .scroll-step {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            text-align: center;
            scroll-snap-align: start;

            h2 {
                font-size: 3rem;
                margin-bottom: 1rem;
            }

            p {
                font-size: 1.4rem;
                max-width: 600px;
            }
        }

        &::-webkit-scrollbar {
            width: 0;
        }
    }
}