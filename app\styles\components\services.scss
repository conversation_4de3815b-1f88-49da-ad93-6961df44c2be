.services {
  padding: 6rem 0;
  color: #ffffff;
  position: relative;
  z-index: 1;

  &__subtitle {
    color: #aaa;
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
  }

  &__grid {
    display: flex;
    flex-wrap: wrap;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 2rem;
    justify-content: center;
  }

  &__card {
    background: rgba(255, 255, 255, 0.05);
    max-width: 30%;
    min-width: 22rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
      border-color: rgba(0, 255, 255, 0.3);
    }

    &-icon {
      margin-bottom: 1.5rem;
      color: rgba(138, 158, 255, 1);
    }

    h3 {
      font-size: 1.25rem;
      margin-bottom: 1rem;
    }

    p {
      font-size: 0.95rem;
      color: #ccc;
    }
  }
}
