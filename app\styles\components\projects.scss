@import '../variables';

.projects {
  position: relative;

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-xl;
  }

  &__card {
    overflow: hidden;
    transition: transform 0.3s ease;
    margin-bottom: 100px;

    &:hover {
      transform: translateY(-4px);
    }

    &-image {
      width: 100%;
      height: 80vh;
      object-fit: cover;
      object-position: top;
    }

    &-content {
      padding: 24px 0px;

      h3 {
        margin-bottom: $spacing-sm;
      }

      p {
        color: $text-secondary;
        margin-bottom: $spacing-md;
      }
    }

    &-tags {
      display: flex;
      gap: $spacing-xs;
      flex-wrap: wrap;

      span {
        background-color: rgba($accent-blue, 0.1);
        color: $accent-blue;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.875rem;
      }
    }

    &-buttons {
      margin-top: 15px;
    }
  }
}