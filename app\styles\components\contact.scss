.contact-section {
  position: relative;
  padding: 80px 20px;
  background: linear-gradient(to bottom, #000000 0%, #001f3f 100%);
  color: #fff;
  text-align: center;
  overflow: hidden;
  width: 100%;

  .contact-overlay {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at center, rgba(0, 122, 255, 0.3), transparent 70%),
      url('data:image/svg+xml;utf8,<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="none" stroke="%231a1a1a" stroke-width="1" /></svg>');
    background-size: cover;
    pointer-events: none;
    z-index: 0;
  }

  .contact-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;

    h1 {
      font-size: 3rem;
      margin-bottom: 20px;

      span {
        background: linear-gradient(90deg, #00c6ff, #0072ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    p {
      font-size: 1.1rem;
      color: #ccc;
      margin-bottom: 40px;
    }

    .contact-form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      input,
      textarea {
        padding: 15px;
        border: none;
        border-radius: 10px;
        font-size: 1rem;
        background: rgba(255, 255, 255, 0.05);
        color: white;
        outline: none;
        transition: background 0.3s;

        &:focus {
          background: rgba(255, 255, 255, 0.1);
        }
      }

      button {
        background: linear-gradient(90deg, #00c6ff, #0072ff);
        border: none;
        padding: 15px;
        font-size: 1rem;
        color: white;
        border-radius: 10px;
        cursor: pointer;
        transition: transform 0.2s;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}