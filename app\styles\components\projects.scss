@import '../variables';

.projects {
  position: relative;

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-xl;
  }

  &__card {
    background-color: $bg-secondary;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease;
    border: 1px solid $border-color;

    &:hover {
      transform: translateY(-4px);
    }

    &-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    &-content {
      padding: $spacing-lg;

      h3 {
        margin-bottom: $spacing-sm;
      }

      p {
        color: $text-secondary;
        margin-bottom: $spacing-md;
      }
    }

    &-tags {
      display: flex;
      gap: $spacing-xs;
      flex-wrap: wrap;

      span {
        background-color: rgba($accent-blue, 0.1);
        color: $accent-blue;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.875rem;
      }
    }
  }
}