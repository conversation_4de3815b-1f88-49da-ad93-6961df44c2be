@import '../variables';

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: transparent;
    border-bottom: 1px solid transparent;
  z-index: 100;
  padding: $spacing-md 0;
    transition: all ease 0.3s;
    box-shadow: none;




  &--scrolled {
    transition: all ease 0.3s;
    background-color: transparent;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #242527;
  }

  &__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: $text-primary;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: $spacing-xs;
  }

  &__nav {
    display: flex;
    gap: $spacing-xl;

    @media (max-width: $breakpoint-md) {
      display: none;
    }
  }

  &__link {
    color: $text-secondary;
    text-decoration: none;
    transition: color 0.3s ease, text-shadow 0.3s ease, transform 0.3s ease;
    position: relative;

    &:hover {
      color: $text-primary;
      text-shadow: 0 0 6px rgba($text-primary, 0.7), 0 0 12px rgba($text-primary, 0.4);
      transform: scale(1.05);
    }

    &--active {
      color: $text-primary;
    }
  }

  &__button {
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    cursor: none;
  }
}